"""QBF solver library."""
from typing import List, Tu<PERSON>, Set, Dict, FrozenSet, Optional
from itertools import chain, combinations
from collections import defaultdict
from functools import reduce
from copy import deepcopy
from dataclasses import dataclass, field
from enum import Enum, auto
import sys
import time

import parse

def solve_file(file_content: str) -> str:
    """Solve a QBF given in QDIMACS format."""
    puzzle = parse.parse_qdimacs(file_content)
    return solve(puzzle)


def solve_by_expansion(puzzle: parse.QDimacs) -> str:
    """Solve a QBF puzzle by expansion of variables."""
    # Make a copy of the puzzle so we don't modify the original
    puzzle = puzzle.copy()

    # Expand the quantifiers
    for quantifier in puzzle.quantifiers:
        assert quantifier.bound_variables
        variable_to_expand = quantifier.bound_variables[-1]
        if quantifier.is_forall():
            puzzle = expand_forall(puzzle, variable_to_expand)
        elif quantifier.is_exists():
            puzzle = expand_exists(puzzle, variable_to_expand)
        else:
            raise ValueError(f"Unknown quantifier type: {quantifier.quantifier_type}")
